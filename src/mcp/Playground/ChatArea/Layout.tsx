import styled from '@emotion/styled';
import {ReactNode} from 'react';
import chatBg from '@/assets/mcp/chatBg.png';
import welcomeBg from '@/assets/mcp/welcomeBg.png';
import {mediaQueryCss} from './styles';

const Wrapper = styled.div`
    height: 100%;
    width: 100%;
    background-image: url(${chatBg});
    background-size: auto 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;

    overflow: hidden;

    @media (max-width: 1440px) {
        min-width: 946px;
    }
    @media (min-width: 1440px) {
        min-width: calc(946px + 15%);
    }
`;

const Container = styled.div<{visible: boolean}>`
    background-image: ${props => (props.visible ? 'url(' + welcomeBg + ')' : 'none')};
    background-size: auto 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    height: 100%;
    width: 100%;
    position: relative;
`;

const MessagesWrapper = styled.div`
    height: 100%;
    width: 100%;
    padding-bottom: 80px;
    overflow: auto;
    box-sizing: border-box;
`;

const InputWrapper = styled.div`
    flex: 0;
    width: 100%;
    padding-bottom: 12px;
    position: absolute;
    bottom: 0px;
    left: 0;
    right: 0;
    z-index: 10;
`;

interface Props {
    messagePanel: ReactNode;
    inputPanel: ReactNode;
    hasMessages?: boolean;
}
export default function ChatLayout({messagePanel, inputPanel, hasMessages = false}: Props) {
    return (
        <Wrapper>
            <Container visible={!hasMessages}>
                <MessagesWrapper>
                    <div
                        style={{
                            height: '100%',
                            width: '100vw',
                            position: 'relative',
                            left: '50%',
                            transform: 'translateX(-50%)',
                        }}
                    >
                        <div className={mediaQueryCss}>
                            <div style={{marginLeft: hasMessages ? -50 : 0}}>
                                {messagePanel}
                            </div>
                        </div>
                    </div>
                </MessagesWrapper>
                <InputWrapper>
                    <div className={mediaQueryCss}>
                        {inputPanel}
                    </div>
                </InputWrapper>
            </Container>
        </Wrapper>

    );
}
